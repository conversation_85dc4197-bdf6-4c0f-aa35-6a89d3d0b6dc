<!-- ============================================ -->
<!--                 Navigation                   -->
<!-- ============================================ -->
---
import Logo from '../assets/PureluxusLogo.png';
import Button from './Button.astro';

const currentPath = Astro.url.pathname;
---

<!-- Import Google Fonts for modern cosmetics website -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">

<header id="cs-navigation">
    <div class="cs-container">
        <!--Nav Logo-->
        <!--Logo is turned white in the css with a filter on dark mode-->
        <a href="/" class="cs-logo" aria-label="back to home">
            <img src={Logo.src} alt="logo" width="210" height="auto" aria-hidden="true" decoding="async">
        </a>
        <!--Navigation List-->
        <nav class="cs-nav" role="navigation">
            <!--Mobile Nav Toggle-->
            <button class="cs-toggle" aria-label="mobile menu toggle">
                <div class="cs-box" aria-hidden="true">
                    <span class="cs-line cs-line1" aria-hidden="true"></span>
                    <span class="cs-line cs-line2" aria-hidden="true"></span>
                    <span class="cs-line cs-line3" aria-hidden="true"></span>
                </div>
            </button>
            <!-- We need a wrapper div so we can set a fixed height on the cs-ul in case the nav list gets too long from too many dropdowns being opened and needs to have an overflow scroll. This wrapper acts as the background so it can go the full height of the screen and not cut off any overflowing nav items while the cs-ul stops short of the bottom of the screen, which keeps all nav items in view no matter how mnay there are-->
            <div class="cs-ul-wrapper">
                <ul id="cs-expanded" class="cs-ul" aria-expanded="false">
                    <li class="cs-li">
                        <a href="/" class={`cs-li-link ${currentPath === '/' ? 'cs-active' : ''}`}>
                            Home
                        </a>
                    </li>
                    <li class="cs-li">
                        <a href="/about" class={`cs-li-link ${currentPath === '/about' || currentPath === '/about/' ? 'cs-active' : ''}`}>
                            About
                        </a>
                    </li>
                    <li class="cs-li">
                        <a href="/productoverview" class={`cs-li-link ${currentPath === '/productoverview' || currentPath === '/productoverview/' ? 'cs-active' : ''}`}>
                            Products
                        </a>
                    </li>
                    <li class="cs-li">
                        <a href="/contactsection" class={`cs-li-link ${currentPath === '/contactsection' || currentPath === '/contactsection/' ? 'cs-active' : ''}`}>
                            Contact
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!--Button positioned on the right-->
        <div class="cs-button-wrapper">
            <Button/>
        </div>

        <!--Dark Mode toggle, uncomment button code if you want to enable a dark mode toggle-->
        <!-- <button id="dark-mode-toggle" aria-label="dark mode toggle">
            <svg class="cs-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 480" style="enable-background:new 0 0 480 480" xml:space="preserve"><path d="M459.782 347.328c-4.288-5.28-11.488-7.232-17.824-4.96-17.76 6.368-37.024 9.632-57.312 9.632-97.056 0-176-78.976-176-176 0-58.4 28.832-112.768 77.12-145.472 5.472-3.712 8.096-10.4 6.624-16.832S285.638 2.4 279.078 1.44C271.59.352 264.134 0 256.646 0c-132.352 0-240 107.648-240 240s107.648 240 240 240c84 0 160.416-42.688 204.352-114.176 3.552-5.792 3.04-13.184-1.216-18.496z"/></svg>
            <img class="cs-sun" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fsun.svg" decoding="async" alt="moon" width="15" height="15">
        </button> -->
    </div>
</header>
<style>
    /*-- -------------------------- -->
<---     Mobile Navigation      -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 63.9375rem) {
  body.cs-open {
    overflow: hidden;
  }
  body.scroll #cs-navigation {
    width: 100%;
    max-width: 100%;
    top: 0;
  }
  body.scroll #cs-navigation:before {
    border-radius: 0;
  }
  body.scroll #cs-navigation .cs-ul-wrapper {
    top: 100%;
  }
  #cs-navigation {
    width: 94%;
    max-width: 80rem;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    /* 12px - 24px */
    padding: clamp(0.75rem, 2vw, 1.5rem);
    /* 12px - 24px */
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    position: fixed;
    top: 2rem;
    left: 50%;
    z-index: 10000;
    transform: translateX(-50%);
    transition: top 0.3s, border-radius 0.3s, width 0.3s, max-width 0.3s;
  }
  #cs-navigation:before {
    /* background color */
    content: "";
    width: 100%;
    height: 100%;
    background: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    opacity: 1;
    /* 12px - 24px */
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    display: block;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    transition: transform 0.2s, border-radius 0.3s ease-in-out;
  }
  #cs-navigation.cs-active:before {
    transform: translateX(-50%) scale(1.03);
  }
  #cs-navigation.cs-active .cs-toggle {
    transform: rotate(180deg);
  }
  #cs-navigation.cs-active .cs-ul-wrapper {
    transform: scaleY(1);
    transition-delay: 0.15s;
  }
  #cs-navigation.cs-active .cs-li {
    opacity: 1;
    transform: translateY(0);
  }
  #cs-navigation .cs-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1.5rem;
  }
  #cs-navigation .cs-logo {
    width: auto;
    max-width: 12.5rem;
    height: 2.5rem;
    margin: 0 auto 0 0;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    z-index: 10;
  }
  #cs-navigation .cs-logo img {
    width: auto;
    height: 100%;
    max-height: 2.5rem;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    object-fit: contain;
    transform: scale(2.0);
  }
  #cs-navigation .cs-toggle {
    width: 3.5rem;
    height: 3.5rem;
    margin: 0 0 0 auto;
    background-color: var(--headerColor);
    border: none;
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 10;
    transition: transform 0.6s;
  }
  #cs-navigation .cs-nav {
    /* sends it to the right in the 3rd position */
    order: 3;
  }
  #cs-navigation .cs-contact-group {
    display: none;
    position: relative;
    z-index: 10;
  }
  #cs-navigation .cs-phone {
    font-size: 1rem;
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.3s, color 0.3s;
  }
  #cs-navigation .cs-phone-icon {
    width: 1.5rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-social {
    display: none;
  }
  #cs-navigation .cs-active .cs-line1 {
    top: 50%;
    transform: translate(-50%, -50%) rotate(225deg);
  }
  #cs-navigation .cs-active .cs-line2 {
    top: 50%;
    transform: translate(-50%, -50%) translateY(0) rotate(-225deg);
    transform-origin: center;
  }
  #cs-navigation .cs-active .cs-line3 {
    opacity: 0;
    bottom: 100%;
  }
  #cs-navigation .cs-box {
    /* 24px - 28px */
    width: clamp(1.5rem, 2vw, 1.75rem);
    height: 1rem;
    position: relative;
  }
  #cs-navigation .cs-line {
    width: 100%;
    height: 2px;
    background-color: var(--bodyTextColorWhite);
    border-radius: 2px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  #cs-navigation .cs-line1 {
    top: 0;
    transition: transform 0.5s, top 0.3s, left 0.3s;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
    transform-origin: center;
  }
  #cs-navigation .cs-line2 {
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    transition: top 0.3s, left 0.3s, transform 0.5s;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
  }
  #cs-navigation .cs-line3 {
    bottom: 0;
    transition: bottom 0.3s, opacity 0.3s;
  }
  #cs-navigation .cs-ul-wrapper {
    width: 100%;
    height: auto;
    padding-bottom: 2.4em;
    background-color: #fff;
    border-radius: 0 0 1.5rem 1.5rem;
    position: absolute;
    top: 85%;
    left: 0;
    z-index: -1;
    overflow: hidden;
    transform: scaleY(0);
    transition: transform 0.4s;
    transform-origin: top;
  }
  #cs-navigation .cs-ul {
    width: 100%;
    height: auto;
    max-height: 65vh;
    margin: 0;
    padding: 4rem 0 0 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 1.25rem;
    overflow: scroll;
  }
  #cs-navigation .cs-li {
    text-align: center;
    list-style: none;
    width: 100%;
    margin-right: 0;
    opacity: 0;
    /* transition from these values */
    transform: translateY(-70/16rem);
    transition: transform 0.6s, opacity 0.9s;
  }
  #cs-navigation .cs-li:nth-of-type(1) {
    transition-delay: 0.05s;
  }
  #cs-navigation .cs-li:nth-of-type(2) {
    transition-delay: 0.1s;
  }
  #cs-navigation .cs-li:nth-of-type(3) {
    transition-delay: 0.15s;
  }
  #cs-navigation .cs-li:nth-of-type(4) {
    transition-delay: 0.2s;
  }
  #cs-navigation .cs-li:nth-of-type(5) {
    transition-delay: 0.25s;
  }
  #cs-navigation .cs-li:nth-of-type(6) {
    transition-delay: 0.3s;
  }
  #cs-navigation .cs-li:nth-of-type(7) {
    transition-delay: 0.35s;
  }
  #cs-navigation .cs-li:nth-of-type(8) {
    transition-delay: 0.4s;
  }
  #cs-navigation .cs-li:nth-of-type(9) {
    transition-delay: 0.45s;
  }
  #cs-navigation .cs-li:nth-of-type(10) {
    transition-delay: 0.5s;
  }
  #cs-navigation .cs-li:nth-of-type(11) {
    transition-delay: 0.55s;
  }
  #cs-navigation .cs-li:nth-of-type(12) {
    transition-delay: 0.6s;
  }
  #cs-navigation .cs-li:nth-of-type(13) {
    transition-delay: 0.65s;
  }
  #cs-navigation .cs-li-link {
    /* 16px - 24px */
    font-family: 'Poppins', sans-serif;
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    font-weight: 500;
    line-height: 1.2em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: inline-block;
    position: relative;
    letter-spacing: 0.02em;
  }
  #cs-navigation .cs-li-link.cs-active {
    color: var(--primary);
  }
  #cs-navigation .cs-li-link:hover {
    color: var(--primary);
  }
  #cs-navigation .cs-button-solid {
    display: none;
  }
  #cs-navigation .cs-button-wrapper {
    display: none;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #cs-navigation .cs-contact-group {
    display: block;
  }
}
/*-- -------------------------- -->
<---     Desktop Navigation     -->
<--- -------------------------- -*/
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  body.scroll #cs-navigation {
    width: 100%;
    max-width: 100%;
    border-radius: 0;
    top: 0;
  }
  #cs-navigation {
    width: 94%;
    max-width: 90rem;
    height: 6rem;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    /* 12px - 24px */
    padding: clamp(0.75rem, 2vw, 1.5rem) 0;
    background-color: #fff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    /* 12px - 24px */
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    display: flex;
    align-items: center;
    position: fixed;
    top: 2rem;
    left: 50%;
    z-index: 10000;
    transform: translateX(-50%);
    transition: top 0.3s, border-radius 0.3s, width 0.3s, max-width 0.3s;
  }
  #cs-navigation .cs-container {
    width: 100%;
    max-width: 90rem;
    margin: auto;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;
  }
  #cs-navigation .cs-toggle {
    display: none;
  }
  #cs-navigation .cs-logo {
    width: auto;
    max-width: 12.5rem;
    height: 3rem;
    /* margin-right auto pushes everything away from it to the right */
    margin: 0 auto 0 0;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    z-index: 100;
  }
  #cs-navigation .cs-logo img {
    width: auto;
    height: 100%;
    max-height: 3rem;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    object-fit: contain;
    transform: scale(2.0);
  }
  #cs-navigation .cs-contact-group {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
  }
  #cs-navigation .cs-phone {
    font-size: 1rem;
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.3s, color 0.3s;
  }
  #cs-navigation .cs-phone-icon {
    width: 1.5rem;
    height: auto;
    display: block;
  }
  #cs-navigation .cs-social {
    height: 2rem;
    opacity: 1;
    display: none;
    visibility: visible;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    transition: opacity 0.3s, visibility 0.3s, height 0.3s;
  }
  #cs-navigation .cs-social-link {
    text-decoration: none;
    width: 2rem;
    height: 2rem;
    background-color: #f7f7f7;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s;
  }
  #cs-navigation .cs-social-link:hover {
    background-color: var(--primary);
  }
  #cs-navigation .cs-social-link:hover .cs-social-icon {
    opacity: 1;
    filter: grayscale(1) brightness(10000%);
  }
  #cs-navigation .cs-social-icon {
    width: 0.75rem;
    height: auto;
    opacity: 0.6;
    display: block;
    transition: opacity 0.3s;
  }
  #cs-navigation .cs-ul-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
    /* absolutely positioned to be dead center */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  #cs-navigation .cs-ul {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    /* 20px - 36px */
    gap: clamp(1.25rem, 2.6vw, 2.25rem);
  }
  #cs-navigation .cs-li {
    list-style: none;
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    /* prevent flexbox from squishing it */
    flex: none;
  }
  #cs-navigation .cs-li-link {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5em;
    text-decoration: none;
    margin: 0;
    color: var(--headerColor);
    display: block;
    position: relative;
    transition: color 0.3s;
    letter-spacing: 0.02em;
  }
  #cs-navigation .cs-li-link:hover {
    color: var(--primary);
  }
  #cs-navigation .cs-li-link.cs-active {
    color: var(--primary);
  }
  #cs-navigation .cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875em, 5.5vw, 3.5em);
    text-align: center;
    text-decoration: none;
    min-width: 9.375rem;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 2rem;
    color: #fff;
    background-color: var(--primary);
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: color 0.3s;
  }
  #cs-navigation .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #fff;
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #cs-navigation .cs-button-solid:hover {
    color: var(--headerColor);
  }
  #cs-navigation .cs-button-solid:hover:before {
    width: 100%;
  }
  #cs-navigation .cs-button-wrapper {
    display: flex;
    align-items: center;
    /* prevents flexbox from squishing it */
    flex: none;
  }
}
/* Large Desktop - 1300px */
@media only screen and (min-width: 81.25rem) {
  #cs-navigation .cs-social {
    display: flex;
  }
}
                                
</style>              
<script>// add classes for mobile navigation toggling
    var CSbody = document.querySelector("body");
    const CSnavbarMenu = document.querySelector("#cs-navigation");
    const CShamburgerMenu = document.querySelector("#cs-navigation .cs-toggle");

    CShamburgerMenu.addEventListener('click', function() {
        CShamburgerMenu.classList.toggle("cs-active");
        CSnavbarMenu.classList.toggle("cs-active");
        CSbody.classList.toggle("cs-open");
        // run the function to check the aria-expanded value
        ariaExpanded();
    });

    // checks the value of aria expanded on the cs-ul and changes it accordingly whether it is expanded or not 
    function ariaExpanded() {
        const csUL = document.querySelector('#cs-expanded');
        const csExpanded = csUL.getAttribute('aria-expanded');

        if (csExpanded === 'false') {
            csUL.setAttribute('aria-expanded', 'true');
        } else {
            csUL.setAttribute('aria-expanded', 'false');
        }
    }

        // This script adds a class to the body after scrolling 100px
    // and we used these body.scroll styles to create some on scroll 
    // animations with the navbar
    
    document.addEventListener('scroll', (e) => { 
        const scroll = document.documentElement.scrollTop;
        if(scroll >= 100){
    document.querySelector('body').classList.add('scroll')
        } else {
        document.querySelector('body').classList.remove('scroll')
        }
    });


    // mobile nav toggle code
    const dropDowns = Array.from(document.querySelectorAll('#cs-navigation .cs-dropdown'));
        for (const item of dropDowns) {
            const onClick = () => {
            item.classList.toggle('cs-active')
        }
        item.addEventListener('click', onClick)
        }
</script>               